import Tick from '@/src/assets/svgs/Tick'
import { LearnCollabStackParamsListI } from '@/src/navigation/types'
import CreateCommunityHeader from '@/src/screens/CreateCommunity/components/CreateCommunityHeader'
import { RouteProp, useRoute } from '@react-navigation/native'
import { useState } from 'react'
import { ActivityIndicator, Image, ScrollView, Text, TouchableOpacity, View } from 'react-native'
import TextInput from '@/src/components/TextInput'
import usePeopleList from './useHook'
import { FlatList } from 'react-native-gesture-handler'
import { FetchFollowersDataI } from '@/src/networks/connect/types'
import { AuthorProfileI } from '@/src/networks/content/types'
import { useDispatch, useSelector } from 'react-redux'
import { setMembers, setModerators } from '@/src/redux/slices/community/communitySlice'
import { selectCommunity } from '@/src/redux/selectors/community'

const PeopleList = () => {
    const route = useRoute<RouteProp<LearnCollabStackParamsListI, 'People'>>()
    const [searchQuery, setSearchQuery] = useState<string>('');
    const { type, title, btnText } = route.params
    const { handleSubmit, people, loading, loadMore } = usePeopleList(type, searchQuery);
    const dispatch = useDispatch()
    const communityState = useSelector(selectCommunity);

    // Initialize selectedUsers based on the current context (moderators or members)
    const [selectedUsers, setSelectedUsers] = useState<AuthorProfileI[]>(() => {
        if (title === 'Add moderator') {
            return communityState.moderators || [];
        } else {
            return communityState.members || [];
        }
    });

    const handleSubmitWithTitle = () => {
        handleSubmit(title);
    };

    const toggleUserSelection = (user: AuthorProfileI): void => {
        // Check if user is selected in the other context (prevent deselection if they're in the other list)
        const isSelectedAsModerator = communityState.moderators?.some((u) => u.id === user.id) || false;
        const isSelectedAsMember = communityState.members?.some((u) => u.id === user.id) || false;

        // If we're adding moderators and user is already a member, or vice versa, don't allow deselection
        if (title === 'Add moderator' && isSelectedAsMember) {
            // User is already a member, can't deselect them from moderator view
            return;
        } else if (title !== 'Add moderator' && isSelectedAsModerator) {
            // User is already a moderator, can't deselect them from member view
            return;
        }

        setSelectedUsers((prev) => {
            const isSelected = prev.some((u) => u.id === user.id);
            if (isSelected) {
                const updatedUsers = prev.filter((u) => u.id !== user.id);
                // Dispatch to appropriate Redux action based on title
                if (title === 'Add moderator') {
                    dispatch(setModerators(updatedUsers));
                } else {
                    dispatch(setMembers(updatedUsers));
                }
                return updatedUsers;
            } else {
                const updatedUsers = [...prev, user];
                // Dispatch to appropriate Redux action based on title
                if (title === 'Add moderator') {
                    dispatch(setModerators(updatedUsers));
                } else {
                    dispatch(setMembers(updatedUsers));
                }
                return updatedUsers;
            }
        });
    };

    const isUserSelected = (userId: string): boolean => {
        // Check if user is selected in current context (selectedUsers)
        const isSelectedInCurrentContext = selectedUsers.some((u) => u.id === userId);

        // Also check if user is selected as moderator or member in Redux state
        const isSelectedAsModerator = communityState.moderators?.some((u) => u.id === userId) || false;
        const isSelectedAsMember = communityState.members?.some((u) => u.id === userId) || false;

        return isSelectedInCurrentContext || isSelectedAsModerator || isSelectedAsMember;
    };

    const getDisplayTitle = (profile: AuthorProfileI): string => {
        const designation = profile.designation?.name || '';
        const entity = profile.entity?.name || '';

        if (designation && entity) {
            return `${designation} at ${entity}`;
        }
        return designation || entity || '';
    };

    const renderUserItem = ({ item }: { item: FetchFollowersDataI }) => {
        const { Profile } = item;

        return (
            <TouchableOpacity
                onPress={() => toggleUserSelection(Profile)}
                className="flex-row items-center justify-between py-3"
            >
                <View className="flex-row items-center flex-1">
                    <Image
                        source={{ uri: (Profile.avatar as string) }}
                        className="w-12 h-12 rounded-full"
                        defaultSource={{ uri: 'https://via.placeholder.com/48' }}
                    />
                    <View className="ml-3 flex-1">
                        <Text className="text-base font-medium text-gray-900">
                            {Profile.name}
                        </Text>
                        <Text className="text-sm text-gray-500 mt-1">
                            {getDisplayTitle(Profile)}
                        </Text>
                    </View>
                </View>
                <View
                    className={`w-6 h-6 rounded border-2 items-center justify-center ${isUserSelected(Profile.id)
                        ? 'bg-primaryGreen border-primaryGreen'
                        : 'border-gray-300'
                        }`}
                >
                    {isUserSelected(Profile.id) && (
                        <Tick width={1.5} height={1.5} color="white" />
                    )}
                </View>
            </TouchableOpacity>
        );
    };

    const renderListFooter = () => {
        if (!loading) return null;

        return (
            <View className="py-4 items-center">
                <ActivityIndicator size="small" color="#448600" />
            </View>
        );
    };

    const renderEmptyState = () => {
        if (loading && people.length === 0) {
            return (
                <View className="items-center justify-center py-8">
                    <ActivityIndicator size="large" color="#448600" />
                    <Text className="text-gray-500 mt-2">Loading connections...</Text>
                </View>
            );
        }

        if (people.length === 0) {
            return (
                <View className="items-center justify-center py-8">
                    <Text className="text-gray-500 text-center">
                        {searchQuery ? 'No connections found matching your search' : 'No connections found'}
                    </Text>
                </View>
            );
        }

        return null;
    };

    return (
        <View className="flex-1 bg-white px-4">
            <CreateCommunityHeader currentPage={3} onNext={handleSubmitWithTitle} buttonTitle={btnText ?? "Create"} />
            <Text className="text-xl font-medium my-4">{title ?? "Add people"}</Text>
            <View className="relative mb-6">
                <TextInput
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    placeholder="Enter name"
                    className=" rounded-lg text-base"
                    placeholderTextColor="#9CA3AF"
                />
            </View>

            {selectedUsers.length > 0 && (
                <View className="mb-6">
                    <Text className="text-gray-500 text-sm mb-3">Selected members</Text>
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        className="flex-row"
                        contentContainerStyle={{ paddingRight: 16 }}
                    >
                        {selectedUsers.slice(0, 5).map((user, index) => (
                            <View key={user.id} className={`items-center ${index > 0 ? 'ml-4' : ''}`}>
                                <View className="relative">
                                    <Image source={{ uri: (user.avatar as string) }} className="w-12 h-12 rounded-full" />
                                    <View className="absolute -bottom-1 -right-1 w-6 h-6 bg-primaryGreen rounded items-center justify-center">
                                        <Tick width={1.5} height={1.5} color="white" />
                                    </View>
                                </View>
                                <Text
                                    className="text-sm font-medium text-subLabelGrayMedium mt-2 text-center max-w-16"
                                    numberOfLines={1}
                                >
                                    {user.name.split(' ')[0]}
                                </Text>
                            </View>
                        ))}
                        {selectedUsers.length > 5 && (
                            <View className="items-center ml-4">
                                <View className="relative">
                                    <View className="w-12 h-12 rounded-full bg-gray-200 items-center justify-center">
                                        <Text className="text-gray-600 font-medium">
                                            +{selectedUsers.length - 5}
                                        </Text>
                                    </View>
                                </View>
                                <Text
                                    className="text-sm font-medium text-subLabelGrayMedium mt-2 text-center max-w-16"
                                    numberOfLines={1}
                                >
                                    More
                                </Text>
                            </View>
                        )}
                    </ScrollView>
                </View>
            )}

            <FlatList
                data={people}
                renderItem={renderUserItem}
                keyExtractor={(item) => item.Profile.id}
                contentContainerStyle={{ paddingHorizontal: 16, paddingBottom: 24 }}
                ListEmptyComponent={renderEmptyState}
                ListFooterComponent={renderListFooter}
                onEndReached={loadMore}
                onEndReachedThreshold={0.3}
                showsVerticalScrollIndicator={false}
            />
        </View>
    )
}

export default PeopleList