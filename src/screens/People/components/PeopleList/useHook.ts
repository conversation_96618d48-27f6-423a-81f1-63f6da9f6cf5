import { LearnCollabStackParamsListI } from "@/src/navigation/types"
import { fetchConnectionsAPI } from "@/src/networks/connect/connection"
import { FetchFollowersDataI } from "@/src/networks/connect/types"
import { selectCommunityLoading } from "@/src/redux/selectors/community"
import { selectProfileId } from "@/src/redux/selectors/user"
import { showToast } from "@/src/utilities/toast"
import { useNavigation } from "@react-navigation/native"
import { StackNavigationProp } from "@react-navigation/stack"
import { isRejectedWithValue } from "@reduxjs/toolkit"
import { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import useCreateCommunityWithAvatar from "@/src/screens/CreateCommunity/hooks/useCreateCommunityWithAvatar"
import type { Image } from 'react-native-image-crop-picker'

const usePeopleList = (type: 'create' | 'add', searchQuery: string, avatarFile?: Image | null, isAvatarDeleted?: boolean) => {
  const profileId = useSelector(selectProfileId)
  const [people, setPeople] = useState<FetchFollowersDataI[]>([])
  const [hasMore, setHasMore] = useState(false)
  const loading = useSelector(selectCommunityLoading)
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const { createCommunityWithAvatar } = useCreateCommunityWithAvatar();


  const fetchData = async () => {
    const response = await fetchConnectionsAPI({ profileId });
    setPeople(response.data)
    setHasMore(Boolean(response.hasMore))
  };

  const loadMore = () => {
    if (hasMore && !loading) {
      fetchData()
    }
  }

  useEffect(() => {
    try {
      fetchData();
    } catch (err) {
      showToast({
        type: "error",
        message: "Failed to fetch people"
      })
    }
  }, [searchQuery]);

  const handleSubmit = async () => {
    if (type === 'create') {
      try {
        const result = await createCommunityWithAvatar();
        if (isRejectedWithValue(result)) {
          showToast({
            type: "error",
            message: "Failed to create community"
          })
        } else {
          showToast({
            type: "success",
            message: "Community created successfully"
          })
          navigation.navigate("MyCommunities")
        }
      } catch (error) {
        showToast({
          type: "error",
          message: "Failed to create community"
        })
      }
    }
    else if (type === 'add') {
      // For adding moderators or members, just navigate back to CreateCommunity
      navigation.navigate("CreateCommunity");
      return;
    }
  }

  return {
    handleSubmit,
    people,
    loading,
    loadMore
  }
}

export default usePeopleList