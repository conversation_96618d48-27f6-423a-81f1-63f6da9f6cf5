import { LearnCollabStackParamsListI } from "@/src/navigation/types"
import { fetchConnectionsAPI } from "@/src/networks/connect/connection"
import { FetchFollowersDataI } from "@/src/networks/connect/types"
import { selectCommunityLoading } from "@/src/redux/selectors/community"
import { selectProfileId } from "@/src/redux/selectors/user"
import { createCommunityAsync } from "@/src/redux/slices/community/communitySlice"
import { AppDispatch } from "@/src/redux/store"
import { showToast } from "@/src/utilities/toast"
import { useNavigation } from "@react-navigation/native"
import { StackNavigationProp } from "@react-navigation/stack"
import { isRejectedWithValue } from "@reduxjs/toolkit"
import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

const usePeopleList = (type: 'create' | 'add', searchQuery: string) => {
  const profileId = useSelector(selectProfileId)
  const [people, setPeople] = useState<FetchFollowersDataI[]>([])
  const [hasMore, setHasMore] = useState(false)
  const loading = useSelector(selectCommunityLoading)
  const dispatch = useDispatch<AppDispatch>()
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();


  const fetchData = async () => {
    const response = await fetchConnectionsAPI({ profileId });
    setPeople(response.data)
    setHasMore(Boolean(response.hasMore))
  };

  const loadMore = () => {
    if (hasMore && !loading) {
      fetchData()
    }
  }

  useEffect(() => {
    try {
      fetchData();
    } catch (err) {
      showToast({
        type: "error",
        message: "Failed to fetch people"
      })
    }
  }, [searchQuery]);

  const handleSubmit = async (title?: string) => {
    if (type === 'create') {
      const result = await dispatch(createCommunityAsync());
      if (isRejectedWithValue(result)) {
        showToast({
          type: "error",
          message: "Failed to create community"
        })
      } else {
        showToast({
          type: "success",
          message: "Community created successfully"
        })
        navigation.navigate("MyCommunities")
      }
    }
    else if (type === 'add') {
      navigation.navigate("CreateCommunity");
      return;
    }
  }

  return {
    handleSubmit,
    people,
    loading,
    loadMore
  }
}

export default usePeopleList