import { Text } from 'react-native';
import { View } from 'react-native';
import { Controller, UseFormReturn } from 'react-hook-form';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import TextInput from '@/src/components/TextInput';
import { CommunityFormFieldsI } from './types';
import AvatarPicker from '@/src/screens/EditUserProfile/components/AvatarPicker';
import useCreateCommunityForm from './useForm';
import ModeratorField from '../ModeratorField';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';

const CreateCommunityForm = ({ methods }: { methods: UseFormReturn<CommunityFormFieldsI> }) => {
  const {
    control,
    formState: { errors },
    watch,
  } = methods;

  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const { getAvatarValue, handleAvatarChange, handleAvatarDelete } = useCreateCommunityForm();

  const moderators = watch('moderators') || [];

  const handleModeratorPress = () => {
    navigation.navigate('People', {
      type: 'add',
      title: 'Add moderator',
      btnText: 'Done'
    });
  };

  return (
    <View className="mt-6">
      <Text className="text-xl font-medium">Create Community</Text>
      <View className="flex gap-6 mt-6">

        <Controller
          control={control}
          name="avatar"
          render={() => (
            <AvatarPicker
              value={getAvatarValue()}
              onChange={handleAvatarChange}
              onDelete={handleAvatarDelete}
              size={120}
            />
          )}
        />

        <Controller
          control={control}
          name={'name'}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label={'Name'}
              value={value}
              onChangeText={onChange}
              placeholder={`Enter forum name`}
              error={errors?.name?.message}
            />
          )}
        />
        <Controller
          control={control}
          name={'description'}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label={'Description'}
              value={value}
              onChangeText={onChange}
              placeholder={`Enter forum description`}
              error={errors?.description?.message}
            />
          )}
        />

        <Controller
          control={control}
          name={'moderators'}
          render={() => (
            <ModeratorField
              moderators={moderators}
              onPress={handleModeratorPress}
            />
          )}
        />

      </View>
    </View>
  );
};

export default CreateCommunityForm;
