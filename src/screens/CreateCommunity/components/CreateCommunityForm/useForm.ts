import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useState } from 'react';
import type { Image } from 'react-native-image-crop-picker';
import { AuthorProfileI } from '@/src/networks/content/types';

const schema = z.object({
    name: z.string().min(1, 'Name is required'),
    description: z.string(),
    avatar: z.string().optional().nullable(),
    moderators: z.array(z.any()).optional()
});

type FormValues = z.infer<typeof schema>;

const validateForm = (values: FormValues) => {
    const result = schema.safeParse(values);
    return {
        isValid: result.success,
        errors: result.success ? null : result.error.formErrors.fieldErrors
    };
};

const useCreateCommunityForm = () => {
    const [avatarFile, setAvatarFile] = useState<Image | null>(null);

    const methods = useForm<FormValues>({
        resolver: zodResolver(schema),
        defaultValues: {
            name: "",
            description: "",
            moderators: [],
        },
    });

    const validationStatus = validateForm(methods.watch());

    const getAvatarValue = () => {
        if (avatarFile?.path) return avatarFile.path;
        return 'Placeholder'

    };

    const handleAvatarChange = (image: Image) => {
        setAvatarFile(image);
    };

    const handleAvatarDelete = () => {
        setAvatarFile(null);
        methods.setValue('avatar', null);
    };


    return {
        methods, isValid: validationStatus.isValid,
        errors: validationStatus.errors,
        getAvatarValue,
        handleAvatarChange,
        handleAvatarDelete
    };
};

export default useCreateCommunityForm;