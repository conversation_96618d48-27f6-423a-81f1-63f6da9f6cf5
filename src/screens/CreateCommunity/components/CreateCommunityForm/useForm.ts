import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import type { Image } from 'react-native-image-crop-picker';
import { selectCommunity } from '@/src/redux/selectors/community';
import { setCommunityAvatar, updateCommunity } from '@/src/redux/slices/community/communitySlice';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { showToast } from '@/src/utilities/toast';

const schema = z.object({
    name: z.string().min(1, 'Name is required'),
    description: z.string(),
    avatar: z.string().optional().nullable(),
    moderators: z.array(z.any()).optional()
});

type FormValues = z.infer<typeof schema>;

const useCreateCommunityForm = () => {
    const [avatarFile, setAvatarFile] = useState<Image | null>(null);
    const [isAvatarDeleted, setIsAvatarDeleted] = useState(false);
    const [loading, setLoading] = useState(false);
    const dispatch = useDispatch();
    const communityState = useSelector(selectCommunity);

    const methods = useForm<FormValues>({
        resolver: zodResolver(schema),
        defaultValues: {
            name: communityState.name || "",
            description: communityState.description || "",
            moderators: communityState.moderators || [],
            avatar: communityState.avatar || null,
        },
    });

    useEffect(() => {
        const subscription = methods.watch((value) => {
            dispatch(updateCommunity({
                name: value.name || "",
                description: value.description || "",
                moderators: value.moderators || [],
            }));
        });
        return () => subscription.unsubscribe();
    }, [methods]);

    const getAvatarValue = () => {
        if (avatarFile?.path) return avatarFile.path;
        if (communityState.avatar) return communityState.avatar;
        return 'Placeholder';
    };

    const handleAvatarChange = async (image: Image) => {
        try {
            setLoading(true);
            setAvatarFile(image);
            setIsAvatarDeleted(false);
            methods.setValue('avatar', image.path);
            dispatch(setCommunityAvatar(image.path));
        } catch (error) {
            showToast({
                type: 'error',
                message: 'Error selecting avatar.',
                description: 'Please try again',
            });
        } finally {
            setLoading(false);
        }
    };

    const handleAvatarDelete = async () => {
        try {
            setLoading(true);
            setAvatarFile(null);
            setIsAvatarDeleted(true);
            methods.setValue('avatar', null);
            dispatch(setCommunityAvatar(null));
        } catch (error) {
            showToast({
                type: 'error',
                message: 'Error deleting avatar.',
                description: 'Please try again',
            });
        } finally {
            setLoading(false);
        }
    };

    const uploadAvatarAndGetUrl = async (): Promise<string | null> => {
        if (isAvatarDeleted) {
            return null;
        }

        if (avatarFile && !avatarFile.path?.startsWith('http')) {
            try {
                const extension = avatarFile.mime?.split('/')[1] || 'jpeg';
                const response = await fetchPresignedUrlAPI([extension], 'AVATAR');

                if (Array.isArray(response) && response.length > 0) {
                    const presignedData = response[0];

                    const fileToUpload = {
                        uri: avatarFile.path,
                        type: avatarFile.mime,
                        filename: `community-avatar.${extension}`,
                    };

                    await uploadFileWithPresignedUrl(fileToUpload, presignedData.uploadUrl);
                    return presignedData.accessUrl;
                }
            } catch (error) {
                showToast({
                    type: 'error',
                    message: 'Error uploading avatar.',
                    description: 'Please try again',
                });
                throw error;
            }
        }

        return communityState.avatar || null;
    };

    return {
        methods,
        isValid: methods.formState.isValid,
        errors: methods.formState.errors,
        getAvatarValue,
        handleAvatarChange,
        handleAvatarDelete,
        uploadAvatarAndGetUrl,
        loading,
        isAvatarDeleted,
        avatarFile
    };
};

export default useCreateCommunityForm;