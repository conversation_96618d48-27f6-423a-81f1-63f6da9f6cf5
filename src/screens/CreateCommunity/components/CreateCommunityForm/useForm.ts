import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import type { Image } from 'react-native-image-crop-picker';

import { selectCommunity } from '@/src/redux/selectors/community';
import { setCommunityAvatar, updateCommunity } from '@/src/redux/slices/community/communitySlice';

const schema = z.object({
    name: z.string().min(1, 'Name is required'),
    description: z.string(),
    avatar: z.string().optional().nullable(),
    moderators: z.array(z.any()).optional()
});

type FormValues = z.infer<typeof schema>;

const validateForm = (values: FormValues) => {
    const result = schema.safeParse(values);
    return {
        isValid: result.success,
        errors: result.success ? null : result.error.formErrors.fieldErrors
    };
};

const useCreateCommunityForm = () => {
    const [avatarFile, setAvatarFile] = useState<Image | null>(null);
    const dispatch = useDispatch();
    const communityState = useSelector(selectCommunity);

    const methods = useForm<FormValues>({
        resolver: zodResolver(schema),
        defaultValues: {
            name: communityState.name || "",
            description: communityState.description || "",
            moderators: communityState.moderators || [],
            avatar: communityState.avatar || null,
        },
    });

    useEffect(() => {
        const subscription = methods.watch((value) => {
            dispatch(updateCommunity({
                name: value.name || "",
                description: value.description || "",
                moderators: value.moderators || [],
            }));
        });
        return () => subscription.unsubscribe();
    }, [methods]);

    const validationStatus = validateForm(methods.watch());

    const getAvatarValue = () => {
        if (avatarFile?.path) return avatarFile.path;
        if (communityState.avatar) return communityState.avatar;
        return 'Placeholder';
    };

    const handleAvatarChange = (image: Image) => {
        setAvatarFile(image);
        const avatarUrl = image.path;
        methods.setValue('avatar', avatarUrl);
        dispatch(setCommunityAvatar(avatarUrl));
    };

    const handleAvatarDelete = () => {
        setAvatarFile(null);
        methods.setValue('avatar', null);
        dispatch(setCommunityAvatar(null));
    };

    return {
        methods,
        isValid: validationStatus.isValid,
        errors: validationStatus.errors,
        getAvatarValue,
        handleAvatarChange,
        handleAvatarDelete
    };
};

export default useCreateCommunityForm;