import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch } from 'react-redux';
import Button from '@/src/components/Button';
import { clearAllSelections } from '@/src/redux/slices/entitysearch/searchSlice';
import { resetFormData } from '@/src/redux/slices/question/questionSlice';
import { resetCommunityState } from '@/src/redux/slices/community/communitySlice';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import Close from '@/src/assets/svgs/Close';

const TOTAL_PAGES = 3;

const CreateCommunityHeader = ({
  currentPage,
  buttonTitle,
  onNext,
  onClose,
}: {
  currentPage: number;
  onNext: () => void;
  buttonTitle?: string;
  onClose?: () => void;
}) => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch();
  const handleClose = () => {
    dispatch(resetFormData());
    dispatch(clearAllSelections());
    dispatch(resetCommunityState());
    navigation.navigate('MyCommunities');
  };
  return (
    <View className="flex-row justify-between">
      <View className="flex-row items-center gap-3">
        <Pressable onPress={handleClose}>
          <Close width={2.25} height={2.25} />
        </Pressable>
        <Text className="text-xl font-medium">
          {currentPage}/{TOTAL_PAGES}
        </Text>
      </View>
      <View>
        <Button
          className="rounded-full bg-primaryGreen w-auto px-6"
          onPress={onNext}
          label={buttonTitle ?? 'Next'}
        ></Button>
      </View>
    </View>
  );
};

export default CreateCommunityHeader;
