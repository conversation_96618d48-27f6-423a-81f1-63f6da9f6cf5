import React from 'react';
import { View, Text, TouchableOpacity, Image, ScrollView } from 'react-native';
import { AuthorProfileI } from '@/src/networks/content/types';
import UserAvatar from '@/src/components/UserAvatar';
import { ModeratorFieldPropsI } from './types';

const ModeratorField: React.FC<ModeratorFieldPropsI> = ({ 
  moderators = [], 
  onPress, 
  label = "Moderator" 
}) => {
  const renderModerator = (moderator: AuthorProfileI, index: number) => (
    <View 
      key={moderator.id} 
      className={`flex-row items-center bg-gray-100 rounded-full px-3 py-2 ${index > 0 ? 'ml-2' : ''}`}
    >
      <UserAvatar
        avatarUri={moderator.avatar as string}
        name={moderator.name}
        width={24}
        height={24}
      />
      <Text className="text-sm font-medium text-gray-900 ml-2" numberOfLines={1}>
        {moderator.name}
      </Text>
    </View>
  );

  const renderAddButton = () => (
    <TouchableOpacity
      onPress={onPress}
      className="flex-row items-center bg-gray-100 rounded-full px-3 py-2 border border-dashed border-gray-300"
    >
      <Text className="text-sm text-gray-600 ml-2">Add moderator</Text>
    </TouchableOpacity>
  );

  return (
    <View className="mb-6">
      <Text className="text-base font-semibold text-gray-800 mb-3">{label}</Text>
      <TouchableOpacity
        onPress={onPress}
        className="min-h-16 bg-white border border-borderGrayLight rounded-xl p-4"
      >
        {moderators.length > 0 ? (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ flexGrow: 1 }}
          >
            <View className="flex-row flex-wrap">
              {moderators.map((moderator, index) => renderModerator(moderator, index))}
              {/* <View className="ml-2">
                {renderAddButton()}
              </View> */}
            </View>
          </ScrollView>
        ) : (
          <Text className='my-auto text-borderGray leading-5 text-base'>Select group moderators</Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default ModeratorField;
