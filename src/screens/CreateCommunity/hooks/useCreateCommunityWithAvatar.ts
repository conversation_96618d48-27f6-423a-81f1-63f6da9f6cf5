import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch } from '@/src/redux/store';
import { selectCommunity } from '@/src/redux/selectors/community';
import { createCommunityAsync } from '@/src/redux/slices/community/communitySlice';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { showToast } from '@/src/utilities/toast';
import type { Image } from 'react-native-image-crop-picker';

export const useCreateCommunityWithAvatar = () => {
  const dispatch = useDispatch<AppDispatch>();
  const communityState = useSelector(selectCommunity);

  const createCommunityWithAvatar = async (avatarFile?: Image | null, isAvatarDeleted?: boolean) => {
    try {
      let avatarUrl: string | null = null;
      console.log(avatarFile, avatarFile?.path, 'checking....')
      if (isAvatarDeleted) {
        avatarUrl = null;
      } else if (avatarFile && !avatarFile.path?.startsWith('http')) {
        const extension = avatarFile.mime?.split('/')[1] || 'jpeg';
        const response = await fetchPresignedUrlAPI([extension], 'AVATAR');

        if (Array.isArray(response) && response.length > 0) {
          const presignedData = response[0];

          const fileToUpload = {
            uri: avatarFile.path,
            type: avatarFile.mime,
            filename: `community-avatar.${extension}`,
          };

          await uploadFileWithPresignedUrl(fileToUpload, presignedData.uploadUrl);
          console.log(presignedData, 'avatarHitting...')
          avatarUrl = presignedData.accessUrl;
        }
      } else  {
        console.log('avatarHitted')
        avatarUrl = communityState.avatar || null;
      }
      console.log(avatarUrl, 'avaraat')
      const result = await dispatch(createCommunityAsync({ avatarUrl, isAvatarDeleted }));
      return result;
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error creating community.',
        description: 'Please try again',
      });
      throw error;
    }
  };

  return { createCommunityWithAvatar };
};

export default useCreateCommunityWithAvatar;
