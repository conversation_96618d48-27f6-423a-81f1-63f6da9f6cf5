import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch } from '@/src/redux/store';
import { selectCommunity } from '@/src/redux/selectors/community';
import { createCommunityAsync } from '@/src/redux/slices/community/communitySlice';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { showToast } from '@/src/utilities/toast';
import type { Image } from 'react-native-image-crop-picker';

export const useCreateCommunityWithAvatar = () => {
  const dispatch = useDispatch<AppDispatch>();
  const communityState = useSelector(selectCommunity);

  const createCommunityWithAvatar = async () => {
    try {
      const avatar = communityState.avatar
      const result = await dispatch(createCommunityAsync());
      return result;
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error creating community.',
        description: 'Please try again',
      });
      throw error;
    }
  };

  return { createCommunityWithAvatar };
};

export default useCreateCommunityWithAvatar;
