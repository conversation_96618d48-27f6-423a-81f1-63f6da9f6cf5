import { KeyboardAvoidingView, Platform, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ScrollView } from 'react-native-gesture-handler';
import { useDispatch } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import CreateCommunityForm from './components/CreateCommunityForm';
import CreateCommunityHeader from './components/CreateCommunityHeader';
import { StackNavigationProp } from '@react-navigation/stack';
import useCreateCommunityForm from './components/CreateCommunityForm/useForm';
import { showToast } from '@/src/utilities/toast';
import { updateCommunity, setMembers, setCommunityAvatar } from '@/src/redux/slices/community/communitySlice';

const CreateCommunityScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch();
  const { methods, errors, isValid, uploadAvatarAndGetUrl } = useCreateCommunityForm();
  const fields = methods.watch()

  const handleNext = async () => {
    if (!isValid) {
      let errorMessage = 'Please fill in all required fields:';
      if (errors?.name) {
        errorMessage += '\n- Name is required';
      }
      showToast({
        type: 'error',
        message: errorMessage
      });
      return;
    }

    dispatch(updateCommunity({ ...fields }));
    dispatch(setMembers([]));
    const avatar = await uploadAvatarAndGetUrl();
    console.log(avatar, 'avarattttttttttt');
    dispatch(setCommunityAvatar(avatar));
    navigation.navigate("CommunityRestrictions")
  };

  return (
    <SafeArea>
      <KeyboardAvoidingView
        {...(Platform.OS === 'ios' ? { behavior: 'padding' } : {})}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="always">
          <View className="flex-1 px-5">
            <CreateCommunityHeader currentPage={1} onNext={() => handleNext()} />
            <CreateCommunityForm methods={methods} />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeArea>
  );
};

export default CreateCommunityScreen;
