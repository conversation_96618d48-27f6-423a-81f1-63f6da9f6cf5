import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CommunityStateI, CommunityAccessE, CommunityUpdatePayload, CommunityResponse, CreateCommunityPayload } from './types';
import { createCommunity } from '@/src/networks/community/community';
import { RootState } from '../../store';

const initialState: CommunityStateI = {
  name: '',
  description: '',
  access: 'PUBLIC',
  isRestricted: false,
  restrictions: [],
  members: [],
  moderators: [],
  loading: false,
  error: null,
};

export const createCommunityAsync = createAsyncThunk<CommunityResponse, void, { rejectValue: string }>('community/createCommunity', async (_, { getState, rejectWithValue }) => {
  try {
    const state = getState() as RootState
    const communityState = state.community;
    const payload: CreateCommunityPayload = {
      name: communityState.name,
      // description: communityState.description,
      access: communityState.access,
      isRestricted: communityState.isRestricted,
      // members: communityState.members,
    };
    const response = await createCommunity(payload);

    return response
  } catch (err) {
    return rejectWithValue("Failed to create community");
  }
})


const communitySlice = createSlice({
  name: 'community',
  initialState,
  reducers: {
    updateCommunity: (state, action: PayloadAction<CommunityUpdatePayload>) => {
      return { ...state, ...action.payload };
    },
    resetCommunityState: () => initialState,
    setCommunityName: (state, action: PayloadAction<string>) => {
      state.name = action.payload;
    },
    setCommunityDescription: (state, action: PayloadAction<string>) => {
      state.description = action.payload;
    },
    setCommunityAccess: (state, action: PayloadAction<CommunityAccessE>) => {
      state.access = action.payload;
    },
    setIsRestricted: (state, action: PayloadAction<boolean>) => {
      state.isRestricted = action.payload;
    },
    setRestrictions: (state, action: PayloadAction<string[]>) => {
      state.restrictions = action.payload;
    },
    setMembers: (state, action: PayloadAction<any[]>) => {
      state.members = action.payload;
    },
    setModerators: (state, action: PayloadAction<any[]>) => {
      state.moderators = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(createCommunityAsync.pending, (state) => {
      state.loading = true;
      state.error = null
    })
      .addCase(createCommunityAsync.fulfilled, () => {
        return initialState
      })
      .addCase(createCommunityAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to create community"
      })
  }
});

export const {
  updateCommunity,
  resetCommunityState,
  setCommunityName,
  setCommunityDescription,
  setCommunityAccess,
  setIsRestricted,
  setRestrictions,
  setMembers,
  setModerators,
} = communitySlice.actions;

export default communitySlice.reducer;